import { ref, computed, onUnmounted } from 'vue'
import { getRealTimeData } from '/@/api/dataview/structure'

/**
 * 动态实时数据刷新 Composable
 * 根据数据的时间间隔动态调整查询周期
 */
export function useDynamicRealTimeData() {

  const realTimeData = ref(null)
  const isChartLoading = ref(false)
  const chartHasErrorOrNoData = ref(false)
  const refreshTimer = ref(null)
  const currentParams = ref(null)
  const currentIntervalInfo = ref(null) // 当前间隔信息
  const consecutiveErrors = ref(0) // 连续错误次数
  const maxConsecutiveErrors = 0 // 最大连续错误次数

  // 数据缓存，用于合并重复数据
  const dataCache = ref(new Map())

  /**
   * 格式化时间间隔显示
   * @param {number} intervalMs 时间间隔（毫秒）
   * @returns {string} 格式化后的时间间隔字符串
   */
  const formatInterval = (intervalMs) => {
    if (intervalMs < 1000) {
      return `${intervalMs}毫秒`
    } else if (intervalMs < 60000) {
      return `${(intervalMs / 1000).toFixed(1)}秒`
    } else if (intervalMs < 3600000) {
      return `${(intervalMs / 60000).toFixed(1)}分钟`
    } else {
      return `${(intervalMs / 3600000).toFixed(1)}小时`
    }
  }

  /**
   * 计算数据的时间间隔（毫秒）
   * @param {Array} eventTimeArray 时间戳数组
   * @returns {number} 最小时间间隔（毫秒）
   */
  const calculateMinInterval = (eventTime) => {
    const eventTimeArray = eventTime.map(time => new Date(time).getTime())
    if (!eventTimeArray || eventTimeArray.length < 2) {
      return 60000 // 默认1分钟
    }

    const intervals = []
    for (let i = 1; i < eventTimeArray.length; i++) {
      const interval = Math.abs(eventTimeArray[i] - eventTimeArray[i - 1])
      if (interval > 0) {
        intervals.push(interval)
      }
    }

    if (intervals.length === 0) {
      return 60000 // 默认1分钟
    }

    // 对间隔进行排序，去除异常值
    intervals.sort((a, b) => a - b)

    // 如果只有少量数据点，直接返回最小间隔
    if (intervals.length <= 3) {
      return intervals[0]
    }

    // 计算间隔的众数或最常见的间隔模式
    const intervalCounts = new Map()
    intervals.forEach(interval => {
      // 将相近的间隔归为一类（允许5%的误差）
      let found = false
      for (let [key, count] of intervalCounts) {
        if (Math.abs(interval - key) / key <= 0.05) {
          intervalCounts.set(key, count + 1)
          found = true
          break
        }
      }
      if (!found) {
        intervalCounts.set(interval, 1)
      }
    })

    // 找到出现次数最多的间隔
    let mostCommonInterval = intervals[0]
    let maxCount = 0
    for (let [interval, count] of intervalCounts) {
      if (count > maxCount) {
        maxCount = count
        mostCommonInterval = interval
      }
    }

    return mostCommonInterval
  }

  /**
   * 根据最小时间间隔计算刷新周期
   * @param {number} minInterval 最小时间间隔（毫秒）
   * @returns {number} 刷新周期（毫秒）
   */
  const calculateRefreshInterval = (minInterval) => {
    // 如果最短间隔是毫秒级（小于1秒），设定为1秒刷新
    if (minInterval < 1000) {
      return 1000
    }

    // 如果最短间隔大于等于1000秒，设定动态策略
    if (minInterval >= 1000000) { // 1000秒 = 1000000毫秒
      // 对于超长间隔，使用采集间隔的1/10作为刷新周期，但不超过5分钟
      return Math.min(minInterval / 10, 300000)
    }

    // 其他情况：根据间隔动态调整，刷新频率为采集频率的1/2到1/5之间
    if (minInterval <= 2000) { // 2秒以内
      return 1000 // 最短1秒刷新
    } else if (minInterval <= 10000) { // 10秒以内
      return Math.max(1000, minInterval / 3) // 采集间隔的1/3
    } else if (minInterval <= 60000) { // 1分钟以内
      return Math.max(2000, minInterval / 4) // 采集间隔的1/4
    } else if (minInterval <= 300000) { // 5分钟以内
      return Math.max(5000, minInterval / 5) // 采集间隔的1/5
    } else {
      return Math.max(10000, Math.min(minInterval / 10, 60000)) // 采集间隔的1/10，但不超过1分钟
    }
  }

  /**
   * 合并新数据和现有数据，去除重复
   * @param {Object} existingData 现有数据
   * @param {Object} newData 新数据
   * @returns {Object} 合并后的数据
   */
  const mergeData = (existingData, newData) => {
    if (!existingData || !newData) {
      return newData || existingData
    }

    const merged = { ...existingData }

    // 合并时间数组
    if (newData.eventTime && existingData.eventTime) {
      const timeSet = new Set([...existingData.eventTime, ...newData.eventTime])
      merged.eventTime = Array.from(timeSet).sort((a, b) => a - b)

      // 合并其他数据字段
      Object.keys(newData).forEach(key => {
        if (key !== 'eventTime' && Array.isArray(newData[key])) {
          // 创建时间到数据的映射
          const existingMap = new Map()
          existingData.eventTime.forEach((time, index) => {
            if (existingData[key] && existingData[key][index] !== undefined) {
              existingMap.set(time, existingData[key][index])
            }
          })

          const newMap = new Map()
          newData.eventTime.forEach((time, index) => {
            if (newData[key] && newData[key][index] !== undefined) {
              newMap.set(time, newData[key][index])
            }
          })

          // 合并数据
          merged[key] = merged.eventTime.map(time => {
            return newMap.has(time) ? newMap.get(time) : existingMap.get(time)
          })
        }
      })

      // 限制数据长度，保留最新的100条
      if (merged.eventTime.length > 100) {
        const keepCount = 100
        const startIndex = merged.eventTime.length - keepCount
        merged.eventTime = merged.eventTime.slice(startIndex)

        Object.keys(merged).forEach(key => {
          if (key !== 'eventTime' && Array.isArray(merged[key])) {
            merged[key] = merged[key].slice(startIndex)
          }
        })
      }
    } else {
      // 如果没有现有数据，直接使用新数据
      Object.assign(merged, newData)
    }

    return merged
  }

  /**
   * 获取实时数据
   * @param {string} pointUniqueCode 点位唯一编码
   * @param {number} structureType 结构类型
   * @param {number} isRealTime 是否实时数据
   * @param {boolean} isRefresh 是否为刷新请求
   */
  const fetchRealTimeData = async (pointUniqueCode, structureType, isRealTime, isRefresh = false) => {
    if (!pointUniqueCode) return

    try {
      if (!isRefresh) {
        isChartLoading.value = true
        chartHasErrorOrNoData.value = false
      }

      const res = await getRealTimeData(pointUniqueCode, structureType, isRealTime)

      if (res.data && res.data.eventTime && res.data.eventTime.length > 0) {
        // 重置连续错误计数
        consecutiveErrors.value = 0

        if (isRefresh && realTimeData.value) {
          // 刷新时合并数据
          const beforeCount = realTimeData.value.eventTime.length
          realTimeData.value = mergeData(realTimeData.value, res.data)
          const afterCount = realTimeData.value.eventTime.length
        } else {
          // 首次加载或无现有数据
          realTimeData.value = res.data
          // console.log(`[数据加载] 首次加载数据: ${res.data.eventTime.length}条`)
        }

        chartHasErrorOrNoData.value = false

        // 计算刷新间隔并设置定时器
        if (isRealTime === 0) { // 只有实时数据才需要自动刷新
          const minInterval = calculateMinInterval(res.data.eventTime)
          const refreshInterval = calculateRefreshInterval(minInterval)

          // 保存间隔信息
          currentIntervalInfo.value = {
            collectionInterval: minInterval,
            refreshInterval: refreshInterval,
            collectionIntervalText: formatInterval(minInterval),
            refreshIntervalText: formatInterval(refreshInterval)
          }

          // console.log(`[动态刷新] 数据点数: ${res.data.eventTime.length}, 采集间隔: ${minInterval}ms (${formatInterval(minInterval)}), 刷新周期: ${refreshInterval}ms (${formatInterval(refreshInterval)})`)

          // 清除现有定时器
          if (refreshTimer.value) {
            clearTimeout(refreshTimer.value)
          }

          // 设置新的定时器
          refreshTimer.value = setTimeout(() => {
            // console.log(`[动态刷新] 执行定时刷新 - ${new Date().toLocaleTimeString()}`)
            fetchRealTimeData(pointUniqueCode, structureType, isRealTime, true)
          }, refreshInterval)
        }
      } else {
        // 增加连续错误计数
        consecutiveErrors.value++
        // console.log(`[数据获取] 无数据返回，连续错误次数: ${consecutiveErrors.value}`)

        realTimeData.value = null
        chartHasErrorOrNoData.value = true

        // 如果连续错误次数超过阈值，停止监控
        if (isRealTime === 0 && consecutiveErrors.value >= maxConsecutiveErrors) {
          // console.log(`[监控停止] 连续${maxConsecutiveErrors}次无数据，停止实时监控`)
          stopMonitoring()
        }
      }
    } catch (error) {
      // 增加连续错误计数
      consecutiveErrors.value++
      // console.error(`Failed to fetch real-time data (连续错误次数: ${consecutiveErrors.value}):`, error)

      realTimeData.value = null
      chartHasErrorOrNoData.value = true

      // 如果连续错误次数超过阈值，停止监控
      if (isRealTime === 0 && consecutiveErrors.value >= maxConsecutiveErrors) {
        // console.log(`[监控停止] 连续${maxConsecutiveErrors}次API调用失败，停止实时监控`)
        stopMonitoring()
      }
    } finally {
      if (!isRefresh) {
        isChartLoading.value = false
      }
    }
  }

  /**
   * 开始监控数据
   * @param {string} pointUniqueCode 点位唯一编码
   * @param {number} structureType 结构类型
   * @param {number} isRealTime 是否实时数据
   */
  const startMonitoring = (pointUniqueCode, structureType, isRealTime) => {
    // 停止现有监控
    stopMonitoring()

    // 保存当前参数
    currentParams.value = { pointUniqueCode, structureType, isRealTime }

    // 开始获取数据
    fetchRealTimeData(pointUniqueCode, structureType, isRealTime)
  }

  /**
   * 停止监控
   */
  const stopMonitoring = () => {
    if (refreshTimer.value) {
      clearTimeout(refreshTimer.value)
      refreshTimer.value = null
    }
    currentParams.value = null
    consecutiveErrors.value = 0 // 重置错误计数
    dataCache.value.clear()
  }

  /**
   * 清空数据
   */
  const clearData = () => {
    realTimeData.value = null
    chartHasErrorOrNoData.value = false
    currentIntervalInfo.value = null
    consecutiveErrors.value = 0 // 重置错误计数
    stopMonitoring()
  }

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stopMonitoring()
  })
  return {
    realTimeData,
    isChartLoading,
    chartHasErrorOrNoData,
    currentIntervalInfo,
    startMonitoring,
    stopMonitoring,
    clearData,
    isMonitoring: computed(() => !!currentParams.value)
  }
}
