import { defineStore } from 'pinia'
import { getSingleStructureInfo } from '/@/api/dataview/structure'
import { watch } from 'vue'

interface Structure {
  structureUniqueCode: number
  structureName: string
  structureType?: number
  monitoringScene?: string
  managementUnit?: string
  [key: string]: any // for other potential properties
}

interface FilterCondition {
  id: string // A unique ID to identify the filter, e.g., 'structureType' or 'category'
  path: string[]
  value: any
}

interface DataviewState {
  curPage: string
  curStructureType: string | null // Changed to string to match component logic
  structureCode: number | null
  structureList: Structure[] // 从API获取的原始结构列表
  filterList: FilterCondition[] // 用于在地图上显示的过滤后列表
  roadFilter: { all: boolean; highway: boolean; national: boolean; rural: boolean }
  selectedCategoryItem: any | null // 用于存储Category组件中的选中项
  infoCardPosition: { x: number; y: number } | null
  currentStructureInfo: any | null
  structureInfoLoading: boolean
  selectedStructureId: number | null // 新增：用于从搜索结果中定位的结构ID
}

// 大屏相关状态
export const dataview = defineStore('dataview', {
  state: (): DataviewState => ({
    curPage: 'Overview',
    curStructureType: null, // 默认不选中任何类型
    structureCode: null,
    structureList: [], // 从API获取的原始结构列表
    filterList: [],
    roadFilter: { all: true, highway: true, national: true, rural: true },
    selectedCategoryItem: null, // 用于存储Category组件中的选中项
    infoCardPosition: null,
    currentStructureInfo: null,
    structureInfoLoading: false,
    selectedStructureId: null, // 新增
  }),
  getters: {
    /**
     * 根据 filterList 中的条件，动态计算出用于显示的结构列表。
     * 这是一个 getter，所以它是响应式的，并且会被缓存。
     */
    displayStructureList(state): Structure[] {
      // 如果没有设置任何过滤条件，直接返回完整的原始列表
      if (state.filterList.length === 0) {
        return state.structureList
      }

      // 使用 filter 和 every 来实现多条件"与"逻辑
      return state.structureList.filter((item: Structure) => {
        // `every` 确保 item 必须满足 filterList 中的所有条件
        return state.filterList.every((condition: FilterCondition) => {
          const { path, value } = condition

          // 使用 reduce 沿着路径深入获取 item 中的值
          const nestedValue = path.reduce((currentLevel: any, key: string) => {
            // 安全检查，防止在 null 或 undefined 上读取属性
            return currentLevel ? currentLevel[key] : undefined
          }, item) // 将 item 断言为 any 以便动态访问

          return nestedValue === value
        })
      })
    }
  },
  actions: {
    pushFilterList(filter: FilterCondition) {
      const index = this.filterList.findIndex(f => f.id === filter.id)
      if (index !== -1) {
        this.filterList[index] = filter
      } else {
        this.filterList.push(filter)
      }
    },
    removeFilterList(id: string) {
      this.filterList = this.filterList.filter(f => f.id !== id)
    },
    setFilterList(filter: FilterCondition[]) {
      this.curStructureType = null;
      this.filterList = filter
    },
    async fetchStructureInfo(code: number) {
      if (code === null) {
        this.currentStructureInfo = null
        return
      }
      // If we are already loading, or if the data for the requested code is already here, don't fetch again.
      if (this.structureInfoLoading) return
      if (this.currentStructureInfo && this.currentStructureInfo.structureUniqueCode === code) return

      this.structureInfoLoading = true
      try {
        const res = await getSingleStructureInfo(code)
        this.currentStructureInfo = res.data
      } catch (error) {
        console.error('Failed to fetch structure info:', error)
        this.currentStructureInfo = null
      } finally {
        this.structureInfoLoading = false
      }
    },
    selectStructure(structure: Structure) {
      if (!structure || typeof structure.structureUniqueCode === 'undefined') return;
      this.selectedStructureId = structure.structureUniqueCode;
      this.structureCode = structure.structureUniqueCode; // 触发弹窗
    }
  }
})