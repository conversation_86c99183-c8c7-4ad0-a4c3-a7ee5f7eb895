<template>
  <div class="ml-12 h-10 flex items-center space-x-8">
    <!-- Structure Type Selector -->
    <div class="relative" ref="typeSelectorRef">
      <div @click="toggleTypeDropdown" class="flex items-center cursor-pointer select-none">
        <span class="text-white text-2xl font-medium mr-6 tracking-wide">{{ selectedTypeName }}</span>
        <div class="relative border-2 border-cyan-400 rounded-lg grid place-items-center bg-slate-800/30 backdrop-blur-sm hover:bg-slate-700/40 transition-all duration-200">
          <svg :class="['w-6 h-6 text-cyan-400 transition-transform duration-200', { 'rotate-180': isTypeOpen }]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>
      <div v-if="isTypeOpen" class="absolute top-full left-0 mt-2 w-max z-50">
        <div class="bg-slate-800/95 backdrop-blur-md border-2 border-cyan-400/60 rounded-lg shadow-2xl overflow-y-auto max-h-72">
          <div v-for="option in structureTypes" :key="option.id" @click="selectType(option)" :class="['px-6 py-3 text-white cursor-pointer transition-all duration-150', 'hover:bg-cyan-400/20 hover:text-cyan-300', selectedStructureType === option.id ? 'bg-cyan-400/30 text-cyan-300' : '']">
            {{ option.name }}
          </div>
        </div>
      </div>
    </div>

    <!-- Structure Selector -->
    <div class="relative" ref="structureSelectorRef">
      <div @click="toggleStructureDropdown" class="flex items-center cursor-pointer select-none">
        <span class="text-white text-2xl font-medium mr-6 tracking-wide">{{ selectedStructureName }}</span>
        <div class="relative border-2 border-cyan-400 rounded-lg grid place-items-center bg-slate-800/30 backdrop-blur-sm hover:bg-slate-700/40 transition-all duration-200">
          <svg :class="['w-6 h-6 text-cyan-400 transition-transform duration-200', { 'rotate-180': isStructureOpen }]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>
      <div v-if="isStructureOpen" class="absolute top-full left-0 mt-2 w-max z-50">
        <div class="bg-slate-800/95 backdrop-blur-md border-2 border-cyan-400/60 rounded-lg shadow-2xl">
          <div class="p-2">
            <input
              ref="searchInputRef"
              type="text"
              v-model="searchQuery"
              placeholder="搜索结构物..."
              class="w-full px-3 py-2 bg-slate-700/50 text-white border-none rounded-md focus:ring-2 focus:ring-cyan-400 outline-none"
              @click.stop
            />
          </div>
          <div class="overflow-y-auto max-h-60">
            <div v-for="option in filteredStructureList" :key="option.structureUniqueCode" @click="selectStructure(option)" :class="['px-6 py-3 text-white cursor-pointer transition-all duration-150', 'hover:bg-cyan-400/20 hover:text-cyan-300', structureCode === option.structureUniqueCode ? 'bg-cyan-400/30 text-cyan-300' : '']">
              {{ option.structureName }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { dataview } from '/@/stores/dataview'
import { getStructureData } from '/@/api/dataview/base'

const store = dataview()
const { structureCode } = storeToRefs(store)
const allStructures = ref([])

// Type Selector State
const isTypeOpen = ref(false)
const typeSelectorRef = ref()
const selectedStructureType = ref('all')
const structureTypes = ref([
  { id: 'all', name: '全部' },
  { id: 1, name: '桥梁' },
  { id: 2, name: '隧道' },
  { id: 3, name: '边坡' },
  { id: 4, name: '下穿通道' },
])

// Structure Selector State
const isStructureOpen = ref(false)
const structureSelectorRef = ref()
const searchQuery = ref('')
const searchInputRef = ref(null)

onMounted(async () => {
  const res = await getStructureData()
  allStructures.value = res.data
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const typeFilteredList = computed(() => {
  if (selectedStructureType.value === 'all') {
    return allStructures.value
  }
  return allStructures.value.filter(s => s.structureType === selectedStructureType.value)
})

const filteredStructureList = computed(() => {
  if (!searchQuery.value.trim()) {
    return typeFilteredList.value
  }
  return typeFilteredList.value.filter(s =>
    s.structureName.toLowerCase().includes(searchQuery.value.toLowerCase().trim())
  )
})

watch(typeFilteredList, (newList) => {
  console.log('newList', newList);
  
  const currentSelectionExists = newList.some(item => item.structureUniqueCode === structureCode.value);
  if (!currentSelectionExists && newList.length > 0) {
    structureCode.value = newList[0].structureUniqueCode;
  } else if (newList.length === 0) {
    structureCode.value = undefined;
  }
}, { immediate: false });


const selectedTypeName = computed(() => structureTypes.value.find(t => t.id === selectedStructureType.value)?.name || '选择类别')

const selectedStructureName = computed(() => {
  if (!structureCode.value) return filteredStructureList.value.length > 0 ? '请选择结构物' : '无可用结构物'
  const selected = allStructures.value.find(s => s.structureUniqueCode === structureCode.value)
  return selected ? selected.structureName : '请选择结构物'
})

// Dropdown Toggle Logic
const toggleTypeDropdown = () => {
  isTypeOpen.value = !isTypeOpen.value
  isStructureOpen.value = false
}
const toggleStructureDropdown = () => {
  isStructureOpen.value = !isStructureOpen.value
  isTypeOpen.value = false
  if (isStructureOpen.value) {
    nextTick(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus()
      }
    })
  } else {
    searchQuery.value = ''
  }
}

// Selection Logic
const selectType = (type) => {
  selectedStructureType.value = type.id
  isTypeOpen.value = false
}
const selectStructure = (structure) => {
  structureCode.value = structure.structureUniqueCode
  isStructureOpen.value = false
  searchQuery.value = ''
}

// Close on click outside
const handleClickOutside = (event) => {
  if (typeSelectorRef.value && !typeSelectorRef.value.contains(event.target)) {
    isTypeOpen.value = false
  }
  if (structureSelectorRef.value && !structureSelectorRef.value.contains(event.target)) {
    isStructureOpen.value = false
    searchQuery.value = ''
  }
}
</script>
<style scoped>
/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
}
::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}
::-webkit-scrollbar-thumb {
  background: rgba(34, 211, 238, 0.5);
  border-radius: 3px;
}
::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 211, 238, 0.7);
}
</style>