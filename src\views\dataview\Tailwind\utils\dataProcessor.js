import { TYPHOON_TYPE_MAPPING } from '../config/constants'

/**
 * 台风数据处理工具类
 */
export class TyphoonDataProcessor {
  /**
   * 处理台风详情数据
   * @param {Array} forecast - 预测数据
   * @returns {Object} 处理后的台风详情
   */
  static processStormDetails(forecast) {
    if (!forecast || forecast.length === 0) {
      return {
        current: {
          location: '--',
          pressure: '-- hPa',
          windSpeed: '-- m/s',
          type: '--'
        },
        forecast12h: { location: '--', wind: '--' },
        forecast24h: { location: '--', wind: '--' },
        forecast48h: { location: '--', wind: '--' }
      }
    }

    try {
      // 找到最新的历史数据点作为当前位置
      const historicalPoints = forecast.filter(point => point.isHistorical)
      const current = historicalPoints.length > 0 ? historicalPoints[historicalPoints.length - 1] : forecast[0]

      const result = {
        current: {
          location: current.lat && current.lon
            ? `${current.lat}°N, ${current.lon}°E`
            : '--',
          pressure: current.pressure
            ? `${current.pressure} hPa`
            : '-- hPa',
          windSpeed: current.windSpeed
            ? `${current.windSpeed} m/s`
            : '-- m/s',
          type: current.type || '--'
        },
        forecast12h: { location: '--', wind: '--' },
        forecast24h: { location: '--', wind: '--' },
        forecast48h: { location: '--', wind: '--' }
      }

      // 获取预测数据点（非历史数据）
      const forecastPoints = forecast.filter(point => !point.isHistorical)

      // 按时间排序预测点
      forecastPoints.sort((a, b) => new Date(a.fxTime) - new Date(b.fxTime))

      // 处理12小时预测 - 找到12小时后的预测点
      const currentTime = new Date(current.fxTime || Date.now())
      const forecast12hTime = new Date(currentTime.getTime() + 12 * 60 * 60 * 1000)
      const forecast12hPoint = this.findClosestForecastPoint(forecastPoints, forecast12hTime)

      if (forecast12hPoint) {
        result.forecast12h = {
          location: forecast12hPoint.lat && forecast12hPoint.lon
            ? `${forecast12hPoint.lat}°N, ${forecast12hPoint.lon}°E`
            : '--',
          wind: forecast12hPoint.windSpeed || '--'
        }
      }

      // 处理24小时预测
      const forecast24hTime = new Date(currentTime.getTime() + 24 * 60 * 60 * 1000)
      const forecast24hPoint = this.findClosestForecastPoint(forecastPoints, forecast24hTime)

      if (forecast24hPoint) {
        result.forecast24h = {
          location: forecast24hPoint.lat && forecast24hPoint.lon
            ? `${forecast24hPoint.lat}°N, ${forecast24hPoint.lon}°E`
            : '--',
          wind: forecast24hPoint.windSpeed || '--'
        }
      }

      // 处理48小时预测
      const forecast48hTime = new Date(currentTime.getTime() + 48 * 60 * 60 * 1000)
      const forecast48hPoint = this.findClosestForecastPoint(forecastPoints, forecast48hTime)

      if (forecast48hPoint) {
        result.forecast48h = {
          location: forecast48hPoint.lat && forecast48hPoint.lon
            ? `${forecast48hPoint.lat}°N, ${forecast48hPoint.lon}°E`
            : '--',
          wind: forecast48hPoint.windSpeed || '--'
        }
      }

      return result
    } catch (error) {
      console.error('处理台风详情数据时出错:', error)
      return {
        current: {
          location: '--',
          pressure: '-- hPa',
          windSpeed: '-- m/s',
          type: '--'
        },
        forecast12h: { location: '--', wind: '--' },
        forecast24h: { location: '--', wind: '--' },
        forecast48h: { location: '--', wind: '--' }
      }
    }
  }

  /**
   * 找到最接近目标时间的预测点
   * @param {Array} forecastPoints - 预测点数组
   * @param {Date} targetTime - 目标时间
   * @returns {Object|null} 最接近的预测点
   */
  static findClosestForecastPoint(forecastPoints, targetTime) {
    if (!forecastPoints || forecastPoints.length === 0) {
      return null
    }

    let closestPoint = null
    let minTimeDiff = Infinity

    forecastPoints.forEach(point => {
      const pointTime = new Date(point.fxTime)
      const timeDiff = Math.abs(pointTime.getTime() - targetTime.getTime())

      if (timeDiff < minTimeDiff) {
        minTimeDiff = timeDiff
        closestPoint = point
      }
    })

    return closestPoint
  }

  /**
   * 验证坐标数据
   * @param {number} lat - 纬度
   * @param {number} lon - 经度
   * @returns {boolean} 是否有效
   */
  static validateCoordinates(lat, lon) {
    return !isNaN(lat) && !isNaN(lon) && 
           lat >= -90 && lat <= 90 && 
           lon >= -180 && lon <= 180
  }

  /**
   * 格式化坐标
   * @param {number} lat - 纬度
   * @param {number} lon - 经度
   * @returns {string} 格式化后的坐标
   */
  static formatCoordinates(lat, lon) {
    if (!this.validateCoordinates(lat, lon)) {
      return '--'
    }
    return `${lat}°N, ${lon}°E`
  }

  /**
   * 获取台风等级中文名称
   * @param {string} type - 台风等级代码
   * @returns {string} 中文名称
   */
  static getTyphoonTypeName(type) {
    return TYPHOON_TYPE_MAPPING[type] || type || '--'
  }

  /**
   * 验证台风数据完整性
   * @param {Object} storm - 台风数据
   * @returns {boolean} 是否完整
   */
  static validateStormData(storm) {
    return storm && 
           typeof storm.id === 'string' && 
           typeof storm.name === 'string' && 
           storm.id.length > 0 && 
           storm.name.length > 0
  }

  /**
   * 验证预测数据完整性
   * @param {Array} forecast - 预测数据
   * @returns {boolean} 是否完整
   */
  static validateForecastData(forecast) {
    return Array.isArray(forecast) && 
           forecast.length > 0 && 
           forecast.every(point => 
             point && 
             typeof point === 'object' && 
             (point.lat !== undefined || point.lon !== undefined)
           )
  }

  /**
   * 清理和标准化预测数据
   * @param {Array} forecast - 原始预测数据
   * @returns {Array} 清理后的预测数据
   */
  static cleanForecastData(forecast) {
    if (!Array.isArray(forecast)) {
      return []
    }

    return forecast.filter(point => {
      if (!point || typeof point !== 'object') {
        return false
      }

      const lat = parseFloat(point.lat)
      const lon = parseFloat(point.lon)
      
      return this.validateCoordinates(lat, lon)
    }).map(point => ({
      ...point,
      lat: parseFloat(point.lat),
      lon: parseFloat(point.lon),
      pressure: point.pressure ? parseFloat(point.pressure) : null,
      windSpeed: point.windSpeed ? parseFloat(point.windSpeed) : null
    }))
  }
}

/**
 * 设备数据处理工具类
 */
export class DeviceDataProcessor {
  /**
   * 验证设备数据
   * @param {Object} deviceData - 设备数据
   * @returns {boolean} 是否有效
   */
  static validateDeviceData(deviceData) {
    if (!deviceData || typeof deviceData !== 'object') {
      return false
    }

    const requiredFields = [
      'temperature', 'humidity', 'windSpeed', 'windDirection',
      'pressure', 'rainfall', 'waveHeight', 'visibility'
    ]

    return requiredFields.every(field => 
      deviceData.hasOwnProperty(field) && 
      deviceData[field] !== null && 
      deviceData[field] !== undefined
    )
  }

  /**
   * 格式化设备数值
   * @param {number|string} value - 数值
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化后的数值
   */
  static formatDeviceValue(value, decimals = 1) {
    const num = parseFloat(value)
    if (isNaN(num)) {
      return '--'
    }
    return num.toFixed(decimals)
  }

  /**
   * 获取风向文本
   * @param {number} direction - 风向角度
   * @param {Array} windDirections - 风向数组
   * @returns {string} 风向文本
   */
  static getWindDirectionText(direction, windDirections) {
    if (isNaN(direction) || !Array.isArray(windDirections)) {
      return '未知'
    }

    const normalizedDirection = ((direction % 360) + 360) % 360
    const index = Math.floor(normalizedDirection / 22.5)
    return windDirections[index] || '未知'
  }
}
