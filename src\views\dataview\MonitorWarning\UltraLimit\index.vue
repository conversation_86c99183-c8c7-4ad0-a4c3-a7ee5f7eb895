<template>
  <div class="w-full h-full px-4 bg-[#021132] flex flex-col overflow-hidden">
    <div class="w-full h-10 flex justify-end items-center flex-shrink-0">
      <span class="cursor-pointer border border-white rounded-full px-2 py-1" @click="cancel()">X</span>
    </div>
    <div class="flex-shrink-0 mb-4">
      <SearchHeader
        :search-params="searchParams"
        :structure-type-options="structureTypeOptions"
        :warning-type-options="warningTypeOptions"
        :warning-level-options="warningLevelOptions"
        :status-options="statusOptions"
        @update:searchParams="updateSearchParams"
        @search="handleSearch"
      />
    </div>
    <div class="w-full pb-5 flex-1 bg-[#021132] grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-5 min-h-0">
      <aside class="lg:col-span-1 min-h-0">
        <Record
          :records="warningData || []"
          :is-loading="isLoading"
          :selected-alarm="selectedAlarm"
          @record-select="handleRecordSelect"
        />
      </aside>
      <main class="lg:col-span-2 min-h-0">
        <Detail :record="alarmDetails" :is-loading="isDetailLoading" :is-list-empty="isListEmpty" />
      </main>
    </div>
  </div>
</template>

<script setup>
// @ts-nocheck
import { reactive, toRefs, ref, watch, watchEffect, computed } from 'vue';
import { getWarningRecord, getAlarmInfo } from '/@/api/dataview/other';
import { useAdvancedFilterApi } from '/@/composables/useApi';
import SearchHeader from './SearchHeader.vue';
import Record from './Record.vue';
import Detail from './Detail.vue';
import dayjs from 'dayjs';

const props = defineProps({
  cancel: {
    type: Function,
    default: () => {}
  },
  alarmDate: {
    type: Date,
    default: () => new Date()
  }
});

const { alarmDate } = toRefs(props);

const searchParams = reactive({
  structureType: '',
  managementName: '',
  sourceType: '',
  alarmLevel: '',
  alarmStatus: '',
  structureName: '',
  timeRange: [],
});

// 创建一个 ref 来包装 searchParams，确保响应性
const searchParamsRef = ref(searchParams);

watchEffect(() => {
  if (alarmDate.value) {
    searchParams.timeRange = [
      dayjs(alarmDate.value).startOf('day').toDate(),
      dayjs(alarmDate.value).endOf('day').toDate(),
    ];
  }
});

// Use the advanced composable to fetch data
const { data: warningData, isLoading } = useAdvancedFilterApi(
  getWarningRecord,
  alarmDate,
  searchParamsRef
);

const isListEmpty = computed(() => !isLoading.value && warningData.value && warningData.value.length === 0);
const selectedAlarm = ref(null);
const alarmDetails = ref(null);
const isDetailLoading = ref(false);

watch(warningData, (newData) => {
  if (newData && newData.length > 0) {
    selectedAlarm.value = {
      alarmId: newData[0].alarmId,
      sourceType: newData[0].sourceType,
    };
  } else {
    selectedAlarm.value = null;
    alarmDetails.value = null;
  }
});

watch(selectedAlarm, async (newAlarm) => {
  if (newAlarm) {
    try {
      isDetailLoading.value = true;
      const alarmType = newAlarm.sourceType === '特殊事件' ? 1 : 0;
      const res = await getAlarmInfo({ alarmId: newAlarm.alarmId, alarmType });
      alarmDetails.value = res.data;
    } catch (error) {
      console.error("Failed to fetch alarm details:", error);
      alarmDetails.value = null;
    } finally {
      isDetailLoading.value = false;
    }
  } else {
    alarmDetails.value = null;
  }
});

const handleRecordSelect = (record) => {
  selectedAlarm.value = record;
};

const structureTypeOptions = ref([
  { value: '1', label: '桥梁' },
  { value: '2', label: '隧道' },
  { value: '3', label: '边坡' },
  { value: '4', label: '下穿通道' },
]);

// This should be populated from an API endpoint
const warningTypeOptions = ref([
  { value: '超限预警', label: '超限预警' },
  { value: '特殊事件', label: '特殊事件' },
]);

const warningLevelOptions = ref([
  { value: '0', label: '特殊事件' },
  { value: '1', label: '一级' },
  { value: '2', label: '二级' },
  { value: '3', label: '三级' },
]);

const statusOptions = ref([
  { value: 'Handled', label: '已处理' },
  { value: 'Processing', label: '处理中' },
  { value: 'Unhandled', label: '未处理' },
]);

// 更新搜索参数的函数
const updateSearchParams = (newParams) => {
  Object.assign(searchParams, newParams);
  // 立即更新 searchParamsRef 以触发重新搜索
  searchParamsRef.value = { ...searchParams };
};

// 手动触发搜索的函数
const handleSearch = () => {
  console.log('Manual search triggered with params:', searchParams);
  // 强制触发重新搜索
  searchParamsRef.value = { ...searchParams };
};
</script>

<style scoped>

.head :deep(select ,input,.el-range-input){
  color: aqua;
}
/* 自定义 Element Plus Date Picker 弹出框样式 */
.custom-date-picker-popper {
  background-color: #002451 !important; /* 背景色 */
  border: 1px solid #005099 !important; /* 边框颜色 */
}

.custom-date-picker-popper .el-picker-panel__content .el-date-table th,
.custom-date-picker-popper .el-picker-panel__content .el-date-table td .el-date-table-cell__text,
.custom-date-picker-popper .el-picker-panel__content .el-year-table td .cell,
.custom-date-picker-popper .el-picker-panel__content .el-month-table td .cell {
  color: #ffffff !important; /* 日期文本颜色 */
}

.custom-date-picker-popper .el-picker-panel__content .el-date-table td.today .el-date-table-cell__text {
  color: #409EFF !important; /* 今日日期文本颜色 */
}

.custom-date-picker-popper .el-picker-panel__content .el-date-table td.available:hover .el-date-table-cell__text {
  color: #003366 !important; /* 悬停日期文本颜色 */
  background-color: #005099 !important; /* 悬停日期背景色 */
}

.custom-date-picker-popper .el-picker-panel__content .el-date-table td.current:not(.disabled) .el-date-table-cell__text {
    background-color: #0066FF !important; /* 选中日期背景色 */
    color: #ffffff !important; /* 选中日期文字颜色 */
}

.custom-date-picker-popper .el-date-editor .el-range-input {
  background-color: transparent !important;
  color: #ffffff !important;
}

.custom-date-picker-popper .el-date-editor .el-range-separator {
  color: #ffffff !important;
}

/* 时间选择部分的样式调整 */
.custom-date-picker-popper .el-time-panel {
    background-color: #002451 !important;
    border: 1px solid #005099 !important;
}
.custom-date-picker-popper .el-time-panel__content::before,
.custom-date-picker-popper .el-time-panel__content::after {
    border-color: #005099 !important; /* 时间选择器滚动条区域分割线颜色 */
}
.custom-date-picker-popper .el-time-spinner__item {
    color: #ffffff !important; /* 时间数字颜色 */
}
.custom-date-picker-popper .el-time-spinner__item:hover:not(.disabled):not(.active) {
    background-color: #005099 !important; /* 时间数字悬停背景 */
    color: #ffffff !important;
}
.custom-date-picker-popper .el-time-spinner__item.active:not(.disabled) {
    color: #409EFF !important; /* 选中时间数字颜色 */
}
.custom-date-picker-popper .el-time-panel__footer .el-button {
    color: #ffffff !important;
    background-color: transparent;
}
.custom-date-picker-popper .el-time-panel__footer .el-button.el-button--primary {
    color: #409EFF !important;
}
</style>
