
<script setup lang='ts'>
import Tabs from './Tabs.vue'
import { formatDate } from '/@/utils/formatTime';
import { ElMessage, ElDialog } from 'element-plus';
import screenfull from 'screenfull';
import { nextTick, watch } from 'vue';
import {getTyphoonList} from '/@/api/dataview/other'
const weatherIcon = ref(100)
const weatherText = ref('多云')
const temperature = ref(35)
const windDirection = ref('东南风')
const windScale = ref('2')
const currentTime = ref(new Date())

// 台风弹窗控制
const tailwindDialogVisible = ref(false)
const TailwindComponent = defineAsyncComponent(() => import('../Tailwind/index.vue'))

// 全屏状态
const state = reactive({
  isScreenfull: false
})

let timer: number | null = null
let isTailWind = ref(false)
const fetchWeatherData = async () => {
  try {
    const response = await fetch('https://devapi.qweather.com/v7/weather/now?location=121.596116,29.866351&key=005ad0bad9f442e6ac4df7d29a73ffa5')
    const data = await response.json()
    if (data.code === '200') {
      weatherIcon.value = data.now.icon
      weatherText.value = data.now.text
      temperature.value = data.now.temp
      windDirection.value = data.now.windDir
      windScale.value = data.now.windScale
    } else {
      console.error('获取天气数据失败:', data.message)
    }
  } catch (error) {
    console.error('获取天气数据出错:', error)
  }
}

onMounted(() => {
  // 设置定时器，每秒更新一次时间
  timer = window.setInterval(() => {
    currentTime.value = new Date()
  }, 1000)

  // 获取天气数据
  fetchWeatherData()
  getTyphoonList().then((res) => {
    isTailWind.value = res.length > 0
  })
})

onUnmounted(() => {
  // 清理定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

// 监听台风弹窗关闭，移除全屏样式类
watch(tailwindDialogVisible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时移除全屏样式类
    const overlay = document.querySelector('.typhoon-fullscreen-overlay')
    if (overlay) {
      overlay.classList.remove('typhoon-fullscreen-overlay')
    }
  }
})

const router = useRouter()

const backHome = () => {
  router.push('/home')
}
const openTailWind = () => {
  tailwindDialogVisible.value = true
  // 延迟添加全屏样式类，确保弹窗已经渲染
  nextTick(() => {
    const overlay = document.querySelector('.el-overlay')
    if (overlay) {
      overlay.classList.add('typhoon-fullscreen-overlay')
    }
  })
}
// 全屏点击时
const onScreenfullClick = () => {
  if (!screenfull.isEnabled) {
    ElMessage.warning('暂不支持全屏');
    return false;
  }
  screenfull.toggle();
  screenfull.on('change', () => {
    if (screenfull.isFullscreen) state.isScreenfull = true;
    else state.isScreenfull = false;
  });
}
</script>

<template>
  <header class="relative animate__animated animate__fadeInDown">
    <div class="look_up">
      <div class="rectangle_21"></div>
      <Tabs />
      <!-- 全屏按钮 -->
      <div class="layout-navbars-breadcrumb-user-icon" @click="onScreenfullClick">
        <i class="iconfont" :title="state.isScreenfull ? '退出全屏' : '进入全屏'" :class="!state.isScreenfull ? 'icon-fullscreen' : 'icon-tuichuquanping'"></i>
      </div>
      <div class="flexcontainer">
        <div class="vector_306">
          <div class="flexcontainer_1">
            <img class="vector_308" src="/image/Vector 308.png" /><img class="vector_307" src="/image/Vector 307.png" />
            <div class="frame_258">
              <img class="vector_307_1" src="/image/Vector 307(1).png" /><img class="frame_260" src="/image/Frame 260.png" />
            </div>

            <div class="absolute right-1 top-8 z-[9999995] flex">
              <div @click="openTailWind()" class="cursor-pointer text-red-500 w-6 mr-6"><img :src="isTailWind ? '/image/icons/台风.png' : '/image/icons/台风0.png'" ></div>
              <div class=" flex gap-6 items-center">
              <!-- 天气图标 -->
              <img class="w-6 h-6" :src='"/yanghu/weather/" + weatherIcon + ".png"' alt="">
              <p class="text-sky-400 text-[15px] ">{{ weatherText }}</p>
              <p class="text-[14px] text-white">{{ temperature }}℃</p>
              <p class="text-[14px] text-white">{{ windDirection }} {{ windScale }}级</p>
              <!-- 显示当前时间xx年xx月xx日 -->
              <span class="text"> {{ formatDate(currentTime, 'YYYY-mm-dd HH:MM:SS WWW') }}</span>
            </div>
            </div>
            
          </div>
          <img class="vector_309" src="/image/Vector 309.png" />
        </div>
        <div class="mask_group">
          <div class="rectangle_20">
            <div class="rectangle_20_1">
              <img class="light" src="/image/光.png" />
            </div>
          </div>
          <img @click="backHome()" class="group_1720_1 hover:cursor-pointer" src="/image/组 1720 1.png" /><img class="rectangle_15" src="/image/Rectangle 15.png" />
        </div>
        <img class="frame_256" src="/image/Frame 256.png" />
      </div>
    </div>
  </header>

  <!-- 台风弹窗 -->
  <el-dialog
    v-model="tailwindDialogVisible"
    width="100%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    fullscreen
    class="tailwind-dialog"
  >
    <TailwindComponent v-if="tailwindDialogVisible" @close="tailwindDialogVisible = false" />
  </el-dialog>
</template>

<style>
/* 抬头 */
.look_up {
  position: absolute;
  width: 1920px;
  height: 88px;
  top: 0;
  left: 0;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
  text-align: center;
  font-size: 16px;
  font-family: 'Microsoft YaHei';
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0.04em;
  color: #2bccff;
  white-space: pre;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* Rectangle 21 */
.rectangle_21 {
  width: 1920px;
  height: 88px;
  position: relative;
  background-color: #021132;
  margin-left: auto;
  margin-right: auto;
  flex-shrink: 0;
  margin-top: 0;
}

/* flexContainer */
.flexcontainer {
  position: relative;
  display: flex;
  width: 1921px;
  height: 124px;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
  flex-shrink: 0;
  margin-top: -88px;
  margin-left: 0;
}

/* Vector 306 */
.vector_306 {
  position: relative;
  width: 1921px;
  height: 78px;
  margin-left: auto;
  background: url('/image/Vector 306.png') center / cover no-repeat;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
  z-index: 0;
  margin-top: 0;
  margin-right: auto;
}

/* flexContainer */
.flexcontainer_1 {
  position: relative;
  display: flex;
  width: 1325.5px;
  height: 62px;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
  z-index: 10;
  margin-top: 14px;
  margin-left: 576px;
}

/* Vector 308 */
.vector_308 {
  width: 762px;
  height: 54px;
  position: relative;
  margin-left: calc(50% - 658.75px);
  margin-right: auto;
  z-index: 1;
  margin-top: 0;
}

/* Vector 307 */
.vector_307 {
  width: 769px;
  height: 54px;
  position: relative;
  margin-left: calc(50% - 662.75px);
  margin-right: auto;
  z-index: 0;
  margin-top: -49px;
}

/* Frame 258 */
.frame_258 {
  position: relative;
  width: 552.5px;
  height: 48px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  z-index: 2;
  margin-top: -45px;
  margin-left: 747px;
}

/* Vector 307 */
.vector_307_1 {
  width: 553px;
  height: 2px;
  position: relative;
  flex-shrink: 0;
  margin-left: 0;
  margin-top: -1px;
}

/* Frame 260 */
.frame_260 {
  width: 105px;
  height: 96px;
  position: relative;
  flex-shrink: 0;
  margin-left: -552.5px;
  margin-top: -48px;
}

/* 2025年3月18日 15:00:17 */
.text {
  z-index: 3;
}

/* Vector 309 */
.vector_309 {
  width: 1921px;
  height: 54px;
  position: relative;
  margin-left: auto;
  z-index: 0;
  margin-top: -52px;
  margin-right: auto;
}

/* Mask group */
.mask_group {
  position: relative;
  width: 638px;
  height: 88px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
  z-index: 2;
  margin-top: -78px;
  margin-left: 641px;
}

/* Rectangle 20 */
.rectangle_20 {
  width: 590px;
  height: 88px;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: -1; /* 改为负值，让它在后面 */
  margin-top: 0;
  -webkit-mask-image: linear-gradient(270deg, rgba(2, 17, 50, 0) 9.47%, #021132 64.94%, rgba(2, 17, 50, 0) 107.24%);
}

/* Rectangle 20 */
.rectangle_20_1 {
  position: relative;
  width: 658px;
  height: 88px;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 0;
  -webkit-mask-image: linear-gradient(180deg, #021132 0%, #021132 80%, rgba(2, 17, 50, 0) 100%);
}

/* 光 */
.light {
  width: 780px;
  height: 193px;
  position: absolute;
  top: 0;
  left: 50%;
  right: auto;
  margin-left: -390px;
}

/* 组 1720 1 */
.group_1720_1 {
  width: 582px;
  height: 34px;
  position: relative;
  object-fit: cover;
  margin-left: auto;
  margin-right: auto;
  z-index: 2;
  margin-top: -70px;
}

/* Rectangle 15 */
.rectangle_15 {
  width: 375px;
  height: 3px;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
  margin-top: 22px;
}

/* Frame 256 */
.frame_256 {
  width: 695px;
  height: 96px;
  z-index: 1;
  position: relative;
  margin-top: -60px;
  margin-left: 43px;
}

/* 全屏按钮样式 - 参考layout样式 */
.layout-navbars-breadcrumb-user-icon {
  position: absolute;
  left: 520px;
  top: 25px;
  z-index: 40;
  padding: 0 10px;
  cursor: pointer;
  color: #2bccff;
  height: 50px;
  line-height: 50px;
  display: flex;
  align-items: center;
}

.layout-navbars-breadcrumb-user-icon:hover {
  background: rgba(43, 204, 255, 0.1);
}

.layout-navbars-breadcrumb-user-icon:hover i {
  display: inline-block;
  animation: logoAnimation 0.3s ease-in-out;
}

@keyframes logoAnimation {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

/* 台风弹窗样式 - 使用最高优先级覆盖全局样式 */

/* 为台风弹窗添加特殊类名来标识 */
.typhoon-fullscreen-overlay.el-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 9999 !important;
}

/* 弹窗容器全屏 */
.typhoon-fullscreen-overlay.el-overlay .el-overlay-dialog {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  align-items: stretch !important;
  justify-content: stretch !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 弹窗本体全屏 */
.tailwind-dialog.el-dialog {
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  max-width: 100% !important;
  width: 100% !important;
  height: 100% !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  box-shadow: none !important;
  border: none !important;
  overflow: hidden !important;
}

/* 隐藏弹窗标题栏 */
.tailwind-dialog.el-dialog .el-dialog__header {
  display: none !important;
}

/* 弹窗内容区域全屏 */
.tailwind-dialog.el-dialog .el-dialog__body {
  width: 100% !important;
  padding: 0 !important;
  height: 100% !important;
  overflow-y: auto !important;
  margin: 0 !important;
  max-height: 100% !important;
  position: relative !important;
}

/* 隐藏关闭按钮 */
.tailwind-dialog.el-dialog .el-dialog__headerbtn {
  display: none !important;
}

/* 确保弹窗内容不被其他元素遮挡 */
.tailwind-dialog.el-dialog .el-dialog__body > * {
  position: relative !important;
  z-index: 1 !important;
}
</style>
